import { useState, useEffect } from "react";
import { Box, Typography, IconButton, MenuItem as MuiMenuItem } from "@mui/material";
import { useNavigate } from 'react-router-dom';
import { Link } from "react-router-dom";
import Home from "@mui/icons-material/Home";
import MoreVertIcon from '@mui/icons-material/MoreVert';
import DescriptionIcon from '@mui/icons-material/Description';
import FolderIcon from '@mui/icons-material/Folder';
import { Sidebar, Menu, MenuItem } from 'react-pro-sidebar';
import { getCurrentUser } from '../../services/api';
import ExLogo from '../../assets/EX-logo-white.png';

const iconStyle = { width: '40px', height: '40px', objectFit: 'contain' };

const Menus = ["Profile Settings", "Log out"];

const Item = ({ title, to, icon, selected, setSelected }) => {
  return (
    <MenuItem
      active={selected === title}
      style={{
        color: "white",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        padding: "10px 16px",
        margin: "15px 0",
        borderRadius: "5px",
        backgroundColor: selected === title ? '#002a54' : 'transparent',
        transition: 'background-color 0.3s ease'
      }}
      onClick={() => setSelected(title)}
      icon={icon}
      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#002a54'}
      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = selected === title ? '#002a54' : 'transparent'}
    >
      <Link to={to} style={{ textDecoration: 'none', color: 'inherit', width: '100%' }}>
        <Typography sx={{ ml: 1, fontSize: '1rem' }}>{title}</Typography>
      </Link>
    </MenuItem>
  );
};

const CustomSidebar = () => {
  const [selected, setSelected] = useState("Dashboard");
  const [open, setOpen] = useState(false);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  // Get current user information from localStorage
  useEffect(() => {
    const getCurrentUserFromStorage = () => {
      try {
        // Get user data from localStorage (stored during login)
        const storedUser = localStorage.getItem('currentUser');
        if (storedUser) {
          const userData = JSON.parse(storedUser);
          setUser(userData);
        } else {
          // Fallback: try to fetch from API with username if available
          const username = localStorage.getItem('currentUsername');
          getCurrentUser(username)
            .then(userData => setUser(userData))
            .catch(error => {
              console.error('Error fetching user data:', error);
              setUser({ first_name: 'User', last_name: '' });
            });
        }
      } catch (error) {
        console.error('Error getting user from storage:', error);
        setUser({ first_name: 'User', last_name: '' });
      } finally {
        setLoading(false);
      }
    };

    getCurrentUserFromStorage();
  }, []);

  const handleProfileSettings = () => {
    navigate('/profile_settings');
  };

  const handleMenuItemClick = (menu) => {
    if (menu === "Profile Settings") {
      handleProfileSettings();
    } else if (menu === "Log out") {
      handleLogout();
    }
    setOpen(false);
  };

  const handleLogout = () => {
    // Clear any local storage or session data if needed
    localStorage.clear();
    sessionStorage.clear();

    // Redirect to login page
    window.location.href = '/login';
  };

  // Get user's full name
  const getUserFullName = () => {
    if (!user) return 'Loading...';

    const firstName = user.first_name || '';
    const lastName = user.last_name || '';
    const fullName = `${firstName} ${lastName}`.trim();

    return fullName || user.username || 'User';
  };

  return (
    <Box
      sx={{
        height: '100vh',
        display: 'flex',
        position: 'relative',
        "& .ps-sidebar-root": {
          background: `#004381 !important`,
          color: "white",
        },
        "& .ps-sidebar-container": {
          background: `#004381 !important`,
          width: '280px',
          display: 'flex',
          flexDirection: 'column',
          position: 'fixed',
          top: 0,
          left: 0,
          bottom: 0,
          overflow: 'hidden',
        },
        "& .ps-inner-item, .ps-menu-item.active": {
          color: "white",
        },
      }}
    >
      <Sidebar>
        <Menu iconShape="square" style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          {/* LOGO */}
          <Box sx={{ padding: '20px', textAlign: 'center' }}>
            <Typography variant="h4" color="white" sx={{ fontSize: '1.5rem' }}>
              E-Inspection 
            </Typography>
          </Box>

          {/* MENU ITEMS */}
          <Box sx={{ flex: 1, padding: '0 10px', paddingTop: '20px', overflowY: 'auto' }}>
            <Item
              title="Tableau de Bord"
              to="/"
              icon={<Home sx={iconStyle} />}
              selected={selected}
              setSelected={setSelected}
            />
            <Item
  title={
    <>
      Formulaire d'inspection <br /> ATEX
    </>
  }
  to="/InspectionFormPage"
  icon={<img src={ExLogo} alt="ATEX Logo" style={iconStyle} />}
  selected={selected}
  setSelected={setSelected}
/>
            <Item
              title="Historique"
              to="/documents"
              icon={<DescriptionIcon sx={iconStyle} />}
              selected={selected}
              setSelected={setSelected}
            />
            <Item
              title={
    <>
      Documents de <br /> Référence
    </>
  }
              to="/reference-documents"
              icon={<FolderIcon sx={iconStyle} />}
              selected={selected}
              setSelected={setSelected}
            />
          </Box>

          {/* PROFILE SECTION - FIXED AT BOTTOM */}
          <Box
            sx={{
              borderTop: '1px solid rgba(255,255,255,0.2)',
              padding: '20px',
              backgroundColor: '#004381',
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              zIndex: 1,
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              <Box sx={{ marginLeft: '10px' }}>
                <Typography sx={{ color: 'white', fontSize: '1.1rem' }}>
                  {loading ? 'Loading...' : getUserFullName()}
                </Typography>
              </Box>
              <IconButton
                sx={{
                  color: 'white',
                  padding: '4px',
                  "&:hover": {
                    backgroundColor: 'rgba(255,255,255,0.1)',
                  },
                }}
                onClick={() => setOpen(!open)}
              >
                <MoreVertIcon sx={{ fontSize: '1.5rem' }} />
              </IconButton>
            </Box>

            {/* DROPDOWN MENU */}
            {open && (
              <Box
                sx={{
                  position: 'absolute',
                  bottom: '100%',
                  right: '20px',
                  backgroundColor: '#004381',
                  borderRadius: '8px',
                  zIndex: 10,
                  boxShadow: '0 4px 12px rgba(0,0,0,0.3)',
                  padding: '10px 0',
                  marginBottom: '10px',
                }}
              >
                {Menus.map((menu) => (
                  <MuiMenuItem
                    key={menu}
                    onClick={() => handleMenuItemClick(menu)}
                    sx={{
                      color: 'white',
                      padding: '8px 16px',
                      '&:hover': {
                        backgroundColor: '#002a54',
                      },
                    }}
                  >
                    {menu}
                  </MuiMenuItem>
                ))}
              </Box>
            )}
          </Box>
        </Menu>
      </Sidebar>
    </Box>
  );
};

export default CustomSidebar;
