from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import InspectionViewSet, UserFileViewSet, CompanyLogoAPIView

router = DefaultRouter()
router.register(r'inspections', InspectionViewSet, basename='inspection')
router.register(r'userfiles', UserFileViewSet, basename='userfile')

urlpatterns = [
    path('', include(router.urls)),
    path('settings/logos/', CompanyLogoAPIView.as_view(), name='company-logos'),
    path('settings/logos/<str:logo_type>/', CompanyLogoAPIView.as_view(), name='company-logo-detail'),
]

print(router.urls)
