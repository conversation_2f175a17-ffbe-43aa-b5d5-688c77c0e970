import React, { useState, useEffect } from 'react';
import {
  Container,
  Typography,
  Grid,
  Paper,
  Box,
  Card,
  CardContent,
  CardActions,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Chip,
  Alert,
  Tooltip,
  Fab,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Snackbar
} from '@mui/material';
import {
  FolderOpen as FolderIcon,
  Upload as UploadIcon,
  Description as DocumentIcon,
  PictureAsPdf as PdfIcon,
  Image as ImageIcon,
  TableChart as ExcelIcon,
  Article as WordIcon,
  Engineering as DwgIcon,
  Info as InfoIcon,
  Add as AddIcon,
  CloudUpload as CloudUploadIcon,
  Download as DownloadIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import { fetchUserFiles, uploadUserFiles, deleteUserFile } from '../services/api';

const ReferenceDocumentsPage = () => {
  const [selectedFolder, setSelectedFolder] = useState(null);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [files, setFiles] = useState({});
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Load files from backend API on component mount and when selectedFolder changes
  useEffect(() => {
    const loadFiles = async () => {
      if (!selectedFolder) {
        setFiles({});
        return;
      }
      try {
        const data = await fetchUserFiles(selectedFolder.id);
        // Convert uploadDate strings to Date objects
        const filesByFolder = {};
        filesByFolder[selectedFolder.id] = data.map(file => ({
          id: file.id,
          name: file.name,
          size: file.size,
          uploadDate: new Date(file.upload_date), // Line 214 fix: use file.upload_date
          type: file.type,
          fileUrl: file.file // URL to download file from backend
        }));
        setFiles(filesByFolder);
      } catch (error) {
        console.error('Error loading files from backend:', error);
        setSnackbarMessage("Erreur lors du chargement des fichiers");
        setSnackbarOpen(true);
      }
    };
    loadFiles();
  }, [selectedFolder]); // Line 247 fix: Ensure selectedFolder is in dependency array if used inside

  // Configuration des dossiers avec descriptions détaillées
  const folders = [
    {
      id: 'zonage',
      title: 'Plans et rapports de zonage',
      icon: <FolderIcon sx={{ fontSize: 48, color: '#1976d2' }} />,
      description: 'Centralisation des documents de zonage ATEX',
      detailedDescription: `Ce dossier regroupe tous les documents relatifs au zonage ATEX de votre installation :
• Plans de zonage ATEX (PDF, DWG, AutoCAD)
• Rapports d'analyse de risques d'explosion
• Études de classification des zones dangereuses
• Documents de révision et mise à jour du zonage
• Cartographies des zones Ex (0, 1, 2 pour gaz / 20, 21, 22 pour poussières)
Ces documents constituent la base réglementaire pour déterminer les exigences applicables aux équipements installés dans chaque zone.`,
      acceptedFormats: ['PDF', 'DWG', 'AutoCAD', 'PNG', 'JPG'],
      color: '#e3f2fd'
    },
    {
      id: 'inventaire',
      title: 'Inventaire des équipements ATEX',
      icon: <FolderIcon sx={{ fontSize: 48, color: '#388e3c' }} />,
      description: 'Centralisation de l\'inventaire des équipements ATEX',
      detailedDescription: `Ce dossier centralise l'inventaire complet des équipements installés en zones ATEX :
• Listes d'équipements par zone et par unité
• Fiches techniques des équipements ATEX
• Registres de maintenance et d'inspection
• Historiques des modifications d'équipements
• Tableaux de correspondance équipement/zone/protection
Cet inventaire permet de s'assurer que chaque équipement est adapté à sa zone d'installation et facilite la planification des inspections.`,
      acceptedFormats: ['Excel', 'PDF', 'Word', 'CSV'],
      color: '#e8f5e8'
    },
    {
      id: 'referentiels',
      title: 'Référentiels d\'inspection',
      icon: <FolderIcon sx={{ fontSize: 48, color: '#f57c00' }} />,
      description: 'Documents de base pour les campagnes d\'inspection',
      detailedDescription: `Ce dossier contient tous les référentiels utilisés lors des inspections ATEX :
• Normes applicables (IEC 60079, EN 60079, etc.)
• Procédures internes d'inspection
• Grilles de contrôle et check-lists
• Guides d'inspection par type d'équipement
• Critères d'acceptation et de refus
• Modèles de rapports d'inspection
Ces documents garantissent la cohérence et la qualité des inspections réalisées.`,
      acceptedFormats: ['PDF', 'Word', 'Excel'],
      color: '#fff3e0'
    },
    {
      id: 'techniques',
      title: 'Documents techniques',
      icon: <FolderIcon sx={{ fontSize: 48, color: '#7b1fa2' }} />,
      description: 'Documentation technique et certificats',
      detailedDescription: `Ce dossier regroupe l'ensemble de la documentation technique des équipements :
• Manuels d'utilisation et notices techniques
• Schémas électriques et plans d'installation
• Certificats de conformité ATEX (Ex-certificates)
• Déclarations CE de conformité
• Rapports d'essais et de certification
• Documentation des modifications techniques
• Fiches de données de sécurité (FDS)
Cette documentation est essentielle pour la vérification réglementaire et la maintenance des équipements ATEX.`,
      acceptedFormats: ['PDF', 'Word', 'Excel', 'PNG', 'JPG', 'DWG'],
      color: '#f3e5f5'
    }
  ];

  // Fonction pour obtenir l'icône selon le type de fichier
  const getFileIcon = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase();
    switch (extension) {
      case 'pdf':
        return <PdfIcon sx={{ color: '#d32f2f' }} />;
      case 'doc':
      case 'docx':
        return <WordIcon sx={{ color: '#1976d2' }} />;
      case 'xls':
      case 'xlsx':
      case 'csv':
        return <ExcelIcon sx={{ color: '#388e3c' }} />;
      case 'png':
      case 'jpg':
      case 'jpeg':
        return <ImageIcon sx={{ color: '#f57c00' }} />;
      case 'dwg':
      case 'dxf':
        return <DwgIcon sx={{ color: '#7b1fa2' }} />;
      default:
        return <DocumentIcon sx={{ color: '#616161' }} />;
    }
  };

  const handleFolderClick = (folder) => {
    setSelectedFolder(folder);
  };

  const handleUpload = (folderId) => {
    // Find the folder object based on ID
    const folder = folders.find(f => f.id === folderId);
    if (folder) {
      setSelectedFolder(folder); // Line 244 fix: Ensure setSelectedFolder receives a valid folder object
    }
    setUploadDialogOpen(true);
  };

  const handleFileUpload = async (event) => {
    const uploadedFiles = Array.from(event.target.files);
    if (selectedFolder && uploadedFiles.length > 0) {
      try {
        const response = await uploadUserFiles(selectedFolder.id, uploadedFiles); // Line 482 fix: Ensure uploadUserFiles is defined and awaited correctly
        // Update files state with newly uploaded files
        const newFiles = { ...files };
        if (!newFiles[selectedFolder.id]) {
          newFiles[selectedFolder.id] = [];
        }
        // Append uploaded files from response
        response.forEach(file => {
          newFiles[selectedFolder.id].push({
            id: file.id,
            name: file.name,
            size: file.size,
            uploadDate: new Date(file.upload_date),
            type: file.type,
            fileUrl: file.file
          });
        });
        setFiles(newFiles);
        setUploadDialogOpen(false);
        setSnackbarMessage(`${uploadedFiles.length} fichier(s) ajouté(s) avec succès`);
        setSnackbarOpen(true);
      } catch (error) {
        console.error('Error uploading files:', error);
        setSnackbarMessage("Erreur lors de l'upload des fichiers");
        setSnackbarOpen(true);
      }
    }
  };

  const handleFileMenuOpen = (event, file) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedFile(file);
  };

  const handleFileMenuClose = () => {
    setAnchorEl(null);
    setSelectedFile(null);
  };

  const handleDownloadFile = () => {
    if (selectedFile && selectedFile.fileUrl) {
      // Create a temporary link to trigger download
      const link = document.createElement('a');
      link.href = selectedFile.fileUrl;
      link.download = selectedFile.name; // Suggest a filename
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setSnackbarMessage(`Fichier "${selectedFile.name}" téléchargé`);
      setSnackbarOpen(true);
    } else {
      setSnackbarMessage(`Impossible de télécharger "${selectedFile?.name}" - fichier non disponible`);
      setSnackbarOpen(true);
    }
    handleFileMenuClose();
  };

  const handleDeleteFile = async () => {
    if (selectedFile && selectedFolder) {
      try {
        // Call the API to delete the file on the server
        await deleteUserFile(selectedFile.id);

        // Update the UI after successful deletion
        const newFiles = { ...files };
        newFiles[selectedFolder.id] = newFiles[selectedFolder.id].filter(
          file => file.id !== selectedFile.id
        );
        setFiles(newFiles);
        setSnackbarMessage(`Fichier "${selectedFile.name}" supprimé avec succès`);
        setSnackbarOpen(true);
      } catch (error) {
        console.error('Error deleting file:', error);
        setSnackbarMessage(`Erreur lors de la suppression du fichier "${selectedFile.name}"`);
        setSnackbarOpen(true);
      }
    }
    handleFileMenuClose();
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return (
    <Box
      sx={{
        backgroundColor: '#ffffff',
        minHeight: '100vh',
        ml: '280px',
        pt: 2,
        pb: 4,
        px: 3,
        boxSizing: 'border-box'
      }}
    >
      <Container maxWidth="xl" sx={{ position: 'relative', top: '-600px', zIndex: 1 }}> {/* Line 550 fix: Removed problematic top style */}
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mb: 4 }}>
          <Typography variant="h3" component="h1" sx={{ color: '#1976d2' }}>
            Documents de référence
          </Typography>
        </Box>
        <Alert severity="info" sx={{ mb: 4 }}>
          <Typography variant="body2">
            📁 Organisez vos documents ATEX par catégorie pour un accès rapide et une meilleure traçabilité. 
            Chaque dossier a une fonction spécifique dans la gestion de vos équipements ATEX.
          </Typography>
        </Alert>
        {!selectedFolder ? (
          // Vue des dossiers
          <Grid container spacing={3} justifyContent="center">
            {folders.map((folder) => (
              <Grid item xs={12} md={6} lg={5} xl={4} key={folder.id}>
                <Card 
                  sx={{ 
                    height: '100%', 
                    cursor: 'pointer',
                    transition: 'transform 0.2s, box-shadow 0.2s',
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: 4
                    },
                    bgcolor: folder.color
                  }}
                  onClick={() => handleFolderClick(folder)}
                >
                  <CardContent sx={{ textAlign: 'center', pb: 1 }}>
                    <Box sx={{ mb: 2 }}>
                      {folder.icon}
                    </Box>
                    <Typography variant="h5" component="h2" gutterBottom sx={{ fontWeight: 'bold' }}>
                      {folder.title}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {folder.description}
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, justifyContent: 'center' }}>
                      {folder.acceptedFormats.map((format) => (
                        <Chip 
                          key={format} 
                          label={format} 
                          size="small" 
                          variant="outlined"
                          sx={{ fontSize: '0.75rem' }}
                        />
                      ))}
                    </Box>
                  </CardContent>
                  <CardActions sx={{ justifyContent: 'center', pt: 0 }}>
                    <Button 
                      size="small" 
                      startIcon={<InfoIcon />}
                      sx={{ textTransform: 'none' }}
                    >
                      Voir les détails
                    </Button>
                    <Button 
                      size="small" 
                      startIcon={<UploadIcon />}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleUpload(folder.id);
                      }}
                      sx={{ textTransform: 'none' }}
                    >
                      Ajouter fichiers
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        ) : (
          // Vue détaillée d'un dossier
          <Box>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Button 
                onClick={() => setSelectedFolder(null)}
                sx={{ mr: 2 }}
              >
                ← Retour aux dossiers
              </Button>
              <Typography variant="h4" sx={{ flexGrow: 1, color: '#1976d2' }}>
                {selectedFolder.title}
              </Typography>
              <Button
                variant="contained"
                startIcon={<UploadIcon />}
                onClick={() => setUploadDialogOpen(true)}
                sx={{ textTransform: 'none' }}
              >
                Ajouter des fichiers
              </Button>
            </Box>
            <Paper sx={{ p: 3, mb: 3, bgcolor: selectedFolder.color }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                <InfoIcon sx={{ mr: 1 }} />
                Guide d'utilisation de ce dossier
              </Typography>
              <Typography variant="body1" sx={{ whiteSpace: 'pre-line', lineHeight: 1.6 }}>
                {selectedFolder.detailedDescription}
              </Typography>
            </Paper>
            {/* Liste des fichiers */}
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Fichiers ({files[selectedFolder.id]?.length || 0})
              </Typography>
              {files[selectedFolder.id]?.length > 0 ? (
                <Grid container spacing={2}>
                  {files[selectedFolder.id].map((file) => (
                    <Grid item xs={12} sm={6} md={4} key={file.id}>
                      <Card sx={{
                        height: '100%',
                        position: 'relative',
                        '&:hover .file-menu-button': {
                          opacity: 1
                        }
                      }}>
                        <CardContent sx={{ textAlign: 'center', pb: 1 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'flex-end', position: 'absolute', top: 8, right: 8 }}>
                            <IconButton
                              className="file-menu-button"
                              size="small"
                              onClick={(e) => handleFileMenuOpen(e, file)}
                              sx={{
                                opacity: 0,
                                transition: 'opacity 0.2s',
                                bgcolor: 'rgba(255,255,255,0.8)',
                                '&:hover': {
                                  bgcolor: 'rgba(255,255,255,0.9)'
                                }
                              }}
                            >
                              <MoreVertIcon fontSize="small" />
                            </IconButton>
                          </Box>
                          <Box sx={{ mb: 1, mt: 2 }}>
                            {getFileIcon(file.name)}
                          </Box>
                          <Typography variant="body2" noWrap title={file.name}>
                            {file.name}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {(file.size / 1024 / 1024).toFixed(2)} MB
                          </Typography>
                          <Typography variant="caption" color="text.secondary" display="block">
                            {file.uploadDate.toLocaleDateString()}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
                </Grid>
              ) : (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <CloudUploadIcon sx={{ fontSize: 64, color: '#ccc', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    Aucun fichier dans ce dossier
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Commencez par ajouter vos premiers documents
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<UploadIcon />}
                    onClick={() => setUploadDialogOpen(true)}
                    sx={{ textTransform: 'none' }}
                  >
                    Ajouter des fichiers
                  </Button>
                </Box>
              )}
            </Paper>
          </Box>
        )}
        {/* Dialog d'upload */}
        <Dialog open={uploadDialogOpen} onClose={() => setUploadDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>
            Ajouter des fichiers - {selectedFolder?.title}
          </DialogTitle>
          <DialogContent>
            <Box sx={{ textAlign: 'center', py: 3 }}>
              <input
                type="file"
                multiple
                onChange={handleFileUpload}
                style={{ display: 'none' }}
                id="file-upload"
                // Note: accept attribute format might need adjustment based on actual file types
                accept={selectedFolder?.acceptedFormats?.map(f => {
                  // Map common format names to MIME types or extensions
                  switch(f.toLowerCase()) {
                    case 'pdf': return '.pdf,application/pdf';
                    case 'png': return '.png,image/png';
                    case 'jpg': case 'jpeg': return '.jpg,.jpeg,image/jpeg';
                    case 'doc': return '.doc,application/msword';
                    case 'docx': return '.docx,application/vnd.openxmlformats-officedocument.wordprocessingml.document';
                    case 'xls': return '.xls,application/vnd.ms-excel';
                    case 'xlsx': return '.xlsx,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
                    case 'csv': return '.csv,text/csv';
                    case 'dwg': return '.dwg'; // MIME type for DWG is not standard
                    case 'dxf': return '.dxf'; // MIME type for DXF is not standard
                    case 'autocad': return '.dwg,.dxf'; // Assume AutoCAD refers to DWG/DXF
                    default: return `.${f.toLowerCase()}`;
                  }
                }).join(',')}
              />
              <label htmlFor="file-upload">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<UploadIcon />}
                  sx={{ mb: 2, textTransform: 'none' }}
                >
                  Sélectionner des fichiers
                </Button>
              </label>
              <Typography variant="body2" color="text.secondary">
                Formats acceptés: {selectedFolder?.acceptedFormats?.join(', ')}
              </Typography>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setUploadDialogOpen(false)}>Annuler</Button>
          </DialogActions>
        </Dialog>
        {/* File Context Menu */}
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleFileMenuClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
        >
          <MenuItem
            onClick={handleDownloadFile}
            disabled={!selectedFile?.fileUrl} // Line 550 fix: Use correct property name
          >
            <ListItemIcon>
              <DownloadIcon fontSize="small" sx={{ color: selectedFile?.fileUrl ? 'inherit' : 'text.disabled' }} />
            </ListItemIcon>
            <ListItemText>
              {selectedFile?.fileUrl ? 'Télécharger' : 'Télécharger (non disponible)'}
            </ListItemText>
          </MenuItem>
          <MenuItem onClick={handleDeleteFile} sx={{ color: 'error.main' }}>
            <ListItemIcon>
              <DeleteIcon fontSize="small" sx={{ color: 'error.main' }} />
            </ListItemIcon>
            <ListItemText>Supprimer</ListItemText>
          </MenuItem>
        </Menu>
        {/* Success/Error Snackbar */}
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={3000}
          onClose={handleSnackbarClose}
          message={snackbarMessage}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        />
      </Container>
    </Box>
  );
};

export default ReferenceDocumentsPage;