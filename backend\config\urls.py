from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import RedirectView

urlpatterns = [
    path('', RedirectView.as_view(url='/api/inspections/')),
    path('admin/', admin.site.urls),
    path('api/auth/', include('authentication.urls')),
    path('api/inspections/', include('inspection.urls')),
] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)