import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Avatar,
  Alert,
  Snackbar,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Business as BusinessIcon,
  Engineering as EngineeringIcon,
  Image as ImageIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import api from '../services/api';

const SettingsMainPage = () => {
  const [logos, setLogos] = useState({
    performing_company: null,
    inspected_company: null
  });
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
  const [deleteDialog, setDeleteDialog] = useState({ open: false, logoType: null });

  useEffect(() => {
    fetchLogos();
  }, []);

  const fetchLogos = async () => {
    try {
      setLoading(true);
      const response = await api.get('/api/inspections/settings/logos/');
      setLogos(response.data);
    } catch (error) {
      console.error('Error fetching logos:', error);
      showSnackbar('Erreur lors du chargement des logos', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleFileUpload = async (event, logoType) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      showSnackbar('Format de fichier non supporté. Utilisez JPG, PNG ou GIF.', 'error');
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      showSnackbar('Le fichier est trop volumineux. Taille maximale: 5MB.', 'error');
      return;
    }

    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('logo', file);
      formData.append('logo_type', logoType);

      const response = await api.post('/api/inspections/settings/logos/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      setLogos(prev => ({
        ...prev,
        [logoType]: response.data[logoType]
      }));

      const companyName = logoType === 'performing_company' 
        ? 'de l\'entreprise réalisatrice' 
        : 'de l\'entreprise inspectée';
      showSnackbar(`Logo ${companyName} ajouté avec succès`, 'success');
    } catch (error) {
      console.error('Error uploading logo:', error);
      showSnackbar('Erreur lors du téléchargement du logo', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteLogo = async (logoType) => {
    try {
      setLoading(true);
      await api.delete(`/api/inspections/settings/logos/${logoType}/`);
      
      setLogos(prev => ({
        ...prev,
        [logoType]: null
      }));

      const companyName = logoType === 'performing_company' 
        ? 'de l\'entreprise réalisatrice' 
        : 'de l\'entreprise inspectée';
      showSnackbar(`Logo ${companyName} supprimé avec succès`, 'success');
      setDeleteDialog({ open: false, logoType: null });
    } catch (error) {
      console.error('Error deleting logo:', error);
      showSnackbar('Erreur lors de la suppression du logo', 'error');
    } finally {
      setLoading(false);
    }
  };

  const showSnackbar = (message, severity) => {
    setSnackbar({ open: true, message, severity });
  };

  const LogoCard = ({ logoType, title, icon, description }) => {
    const logo = logos[logoType];
    
    return (
      <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <CardContent sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            {icon}
            <Typography variant="h6" sx={{ ml: 1 }}>
              {title}
            </Typography>
          </Box>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {description}
          </Typography>

          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 2 }}>
            {logo ? (
              <Avatar
                src={logo}
                sx={{ 
                  width: 120, 
                  height: 120, 
                  border: '2px solid #e0e0e0',
                  borderRadius: 2
                }}
                variant="rounded"
              />
            ) : (
              <Box
                sx={{
                  width: 120,
                  height: 120,
                  border: '2px dashed #ccc',
                  borderRadius: 2,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  backgroundColor: '#f5f5f5'
                }}
              >
                <ImageIcon sx={{ fontSize: 40, color: '#ccc' }} />
              </Box>
            )}
          </Box>
        </CardContent>

        <CardActions sx={{ justifyContent: 'center', p: 2 }}>
          <input
            accept="image/*"
            style={{ display: 'none' }}
            id={`upload-${logoType}`}
            type="file"
            onChange={(e) => handleFileUpload(e, logoType)}
          />
          <label htmlFor={`upload-${logoType}`}>
            <Button
              variant="contained"
              component="span"
              startIcon={<UploadIcon />}
              disabled={loading}
              sx={{ mr: 1 }}
            >
              {logo ? 'Remplacer' : 'Ajouter'}
            </Button>
          </label>
          
          {logo && (
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={() => setDeleteDialog({ open: true, logoType })}
              disabled={loading}
            >
              Supprimer
            </Button>
          )}
        </CardActions>
      </Card>
    );
  };

  return (
<Box
  sx={{
    backgroundColor: '#ffffff',
    ml: '280px',
    pt: 0,
    pb: 4,
    px: 3,
    width: `calc(100% - 280px)`,
    boxSizing: 'border-box',
    marginTop: '-600px'
  }}
>
      <Typography variant="h4" sx={{ color: '#1976d2', mb: 3, mt: 0 }}>
        Paramètres Principaux
      </Typography>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
          <BusinessIcon sx={{ mr: 1 }} />
          Configuration des Logos d'Entreprise
        </Typography>
        
        <Divider sx={{ my: 2 }} />
        
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
          Configurez les logos qui apparaîtront dans les rapports d'inspection PDF. 
          Ces logos seront affichés dans l'en-tête de chaque rapport généré.
        </Typography>

        {loading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
            <CircularProgress />
          </Box>
        )}

        <Grid container spacing={1}>
          <Grid item xs={12} md={6}>
            <LogoCard
              logoType="performing_company"
              title="Entreprise Réalisatrice"
              icon={<EngineeringIcon color="primary" />}
              description="Logo de l'entreprise qui effectue l'inspection. Ce logo apparaîtra dans la première colonne de l'en-tête du rapport."
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <LogoCard
              logoType="inspected_company"
              title="Entreprise Inspectée"
              icon={<BusinessIcon color="secondary" />}
              description="Logo de l'entreprise dont les équipements sont inspectés. Ce logo apparaîtra dans la deuxième colonne de l'en-tête du rapport."
            />
          </Grid>
        </Grid>

        <Alert severity="info" sx={{ mt: 3 }}>
          <Typography variant="body2">
            <strong>Formats supportés:</strong> JPG, PNG, GIF<br />
            <strong>Taille maximale:</strong> 5MB<br />
            <strong>Recommandation:</strong> Utilisez des images carrées ou rectangulaires pour un meilleur rendu
          </Typography>
        </Alert>
      </Paper>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialog.open}
        onClose={() => setDeleteDialog({ open: false, logoType: null })}
      >
        <DialogTitle>Confirmer la suppression</DialogTitle>
        <DialogContent>
          <Typography>
            Êtes-vous sûr de vouloir supprimer ce logo ? Cette action est irréversible.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialog({ open: false, logoType: null })}>
            Annuler
          </Button>
          <Button 
            onClick={() => handleDeleteLogo(deleteDialog.logoType)} 
            color="error"
            disabled={loading}
          >
            Supprimer
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert 
          onClose={() => setSnackbar({ ...snackbar, open: false })} 
          severity={snackbar.severity}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default SettingsMainPage;